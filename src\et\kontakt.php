<?php
// Kontakt | Alimendid.ee

// Include database configuration

// Function to sanitize input data
function sanitize_input($data)
{
  $data = trim($data);
  $data = stripslashes($data);
  $data = htmlspecialchars($data);
  return $data;
}

// Process form data
if ($_SERVER["REQUEST_METHOD"] == "POST") {
  // Sanitize input data
  $eesnimi = isset($_POST['eesnimi']) ? sanitize_input($_POST['eesnimi']) : '';
  $perenimi = isset($_POST['perenimi']) ? sanitize_input($_POST['perenimi']) : '';
  $email = isset($_POST['email']) ? sanitize_input($_POST['email']) : '';
  $telefon = isset($_POST['telefon']) ? sanitize_input($_POST['telefon']) : '';
  $sonum = isset($_POST['sonum']) ? sanitize_input($_POST['sonum']) : '';

  // Validate required fields
  $errors = [];
  if (empty($eesnimi)) $errors[] = "Eesnimi on kohustuslik";
  if (empty($perenimi)) $errors[] = "Perenimi on kohustuslik";
  if (empty($email)) {
    $errors[] = "E-post on kohustuslik";
  } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Palun sisesta korrektne e-posti aadress";
  }
  if (empty($sonum)) $errors[] = "Sõnum on kohustuslik";

  // If there are no errors, process the form
  if (empty($errors)) {
    try {
      // Insert data into database
      $stmt = $pdo->prepare("INSERT INTO kliendid (
                teenus, eesnimi, perenimi, email, telefon, sonum, tingimused, submission_date
            ) VALUES (
                'kontakt', :eesnimi, :perenimi, :email, :telefon, :sonum, 1, NOW()
            )");

      // Bind parameters
      $stmt->bindParam(':eesnimi', $eesnimi);
      $stmt->bindParam(':perenimi', $perenimi);
      $stmt->bindParam(':email', $email);
      $stmt->bindParam(':telefon', $telefon);
      $stmt->bindParam(':sonum', $sonum);

      // Execute the statement
      $stmt->execute();

      // Store success message in session
      $_SESSION['contact_success'] = true;
      $_SESSION['contact_message'] = "Aitäh! Päring on edukalt saadetud. Vastame 1 tööpäeva jooksul.";
    } catch (PDOException $e) {
      // Store error message in session
      $errors[] = "Andmete salvestamisel tekkis viga. Palun proovige hiljem uuesti.";

      // For development, you might want to see the error
      $_SESSION['contact_error_details'] = $e->getMessage();

      // Log error to file
      $error_log = '../logs/error_log.txt';
      $error_message = date('Y-m-d H:i:s') . " - " . $e->getMessage() . "\n";
      file_put_contents($error_log, $error_message, FILE_APPEND);
    }
  }

  // Store form data and errors in session if there are errors
  if (!empty($errors)) {
    $_SESSION['contact_errors'] = $errors;
    $_SESSION['contact_form_data'] = [
      'eesnimi' => $eesnimi,
      'perenimi' => $perenimi,
      'email' => $email,
      'telefon' => $telefon,
      'sonum' => $sonum
    ];
  }
}

// Check for success or error messages
$success_message = isset($_SESSION['contact_success']) ? $_SESSION['contact_message'] : '';
$errors = isset($_SESSION['contact_errors']) ? $_SESSION['contact_errors'] : [];

// Get form data if it exists (for repopulating the form after an error)
$form_data = isset($_SESSION['contact_form_data']) ? $_SESSION['contact_form_data'] : [
  'eesnimi' => '',
  'perenimi' => '',
  'email' => '',
  'telefon' => '',
  'sonum' => ''
];

// Clear session variables
unset($_SESSION['contact_success']);
unset($_SESSION['contact_message']);
unset($_SESSION['contact_errors']);
unset($_SESSION['contact_form_data']);
?>
<!DOCTYPE html>
<html lang="en" class="relative min-h-full light">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8" />
  <meta
    name="robots"
    content="max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
  <link rel="canonical" href="https://alimendid.ee/" />
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <meta
    name="description"
    content="Elatise õigusabi kiiresti ja lihtsalt. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />

  <meta name="twitter:site" content="@alimendidee" />
  <meta name="twitter:creator" content="@alimendidee" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta
    name="twitter:title"
    content="Alimendid.ee · Elatise õigusabi kiiresti ja lihtsalt" />
  <meta
    name="twitter:description"
    content="Elatise õigusabi kiiresti ja lihtsalt. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />
  <meta
    name="twitter:image"
    content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg" />

  <meta property="og:url" content="https://alimendid.ee/" />
  <meta property="og:locale" content="et_EE" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="Alimendid.ee" />
  <meta
    property="og:title"
    content="Alimendid.ee · Elatise õigusabi kiiresti ja lihtsalt" />
  <meta
    property="og:description"
    content="Elatise õigusabi kiiresti ja lihtsalt. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />
  <meta
    property="og:image"
    content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg" />

  <!-- Title -->
  <title>Kontakt · Alimendid.ee</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../assets/failid/favicon/favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link href="../output.css" rel="stylesheet" />
  <link href="../assets/css/main.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="../assets/failid/css/disain.css">


  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>
</head>

<body class="dark:bg-neutral-900">
  <!-- //LINK - MENU -->
  <?php include '../assets/failid/komponendid/et/menu.php'; ?>


  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">

    <!-- Hero -->
    <div class="max-w-6xl px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="pt-10 md:pt-20">
        <!-- Pealkiri -->
        <div class="mb-10 max-w-xl mx-auto text-center">
          <h1 class="text-5xl md:text-5xl dark:text-white font-semibold text-gray-800 ">
            Kontakt
          </h1>

          <p class="mt-2 text-xl md:text-xl text-gray-600 dark:text-gray-200">
            Alimendid.ee kontaktandmed
          </p>
        </div>
        <!-- End Pealkiri -->

      </div>
    </div>
    <!-- End Hero -->

    <!-- Kontaktivorm -->
    <div class="max-w-lg mx-auto px-4" style="display: none">
      <!-- Card -->
      <div class="flex flex-col border border-gray-200 rounded-xl p-4 sm:p-6 lg:p-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-8 text-center">Kontaktivorm</h2>

        <?php if (!empty($success_message)): ?>
          <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline"><?php echo htmlspecialchars($success_message); ?></span>
          </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <?php foreach ($errors as $error): ?>
              <p class="block sm:inline"><?php echo htmlspecialchars($error); ?></p>
            <?php endforeach; ?>
          </div>
        <?php endif; ?>

        <form method="POST" action="kontakt.php">
          <div class="grid gap-4 lg:gap-6">
            <!-- Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6">
              <div>
                <label for="eesnimi" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                  Nimi
                </label>
                <input type="text" name="eesnimi" id="eesnimi" value="<?php echo htmlspecialchars($form_data['eesnimi']); ?>" required class="py-2.5 sm:py-3 px-4 block w-full border-gray-200 rounded-lg sm:text-sm focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none">
              </div>

              <div>
                <label for="email" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                  E-post
                </label>
                <input type="email" name="email" id="email" value="<?php echo htmlspecialchars($form_data['email']); ?>" required autocomplete="email" class="py-2.5 sm:py-3 px-4 block w-full border-gray-200 rounded-lg sm:text-sm focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none">
              </div>
            </div>
            <!-- End Grid -->

            <div>
              <label for="sonum" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">Küsimus</label>
              <textarea id="sonum" name="sonum" rows="4" required class="py-2.5 sm:py-3 px-4 block w-full border-gray-200 rounded-lg sm:text-sm focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none"><?php echo htmlspecialchars($form_data['sonum']); ?></textarea>
            </div>
          </div>
          <!-- End Grid -->

          <div class="mt-6 grid">
            <button type="submit" class="w-full py-3 px-4 inline-flex justify-center items-center gap-x-2 text-lg font-medium rounded-lg border border-transparent bg-orange-500 text-white hover:bg-orange-600 focus:outline-hidden focus:bg-orange-600 disabled:opacity-50 disabled:pointer-events-none">Saada päring</button>
          </div>

          <div class="mt-3 text-center">
            <p class="text-sm text-gray-500">
              Vastame päringule 1 tööpäeva jooksul
            </p>
          </div>
        </form>
      </div>
      <!-- End Kontaktivorm -->
    </div>

    <!-- Kontaktandmed -->
    <div class="mt-12 max-w-4xl mx-auto bg-white rounded-xl p-5 mb-10 sm:px-6 lg:px-8">

      <!-- Redesigned layout with consistent spacing -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
        <!-- First column -->
        <div class="grid grid-cols-1 gap-4">
          <div class="text-center">
            <p class="font-medium text-gray-800">Ärinimi</p>
            <p class="text-gray-600">Alimendid.ee OÜ</p>
          </div>

          <div class="text-center">
            <p class="font-medium text-gray-800">Registrikood</p>
            <p class="text-gray-600">11269840</p>
          </div>

          <div class="text-center">
            <p class="font-medium text-gray-800">Kontaktandmed</p>
            <div class="flex justify-center space-x-4 mt-1">
              <a href="mailto:<EMAIL>" class="text-orange-500 hover:text-orange-600"><EMAIL></a>
            </div>
          </div>
        </div>

        <!-- Second column -->
        <div class="grid grid-cols-1 gap-4">
          <div class="text-center">
            <p class="font-medium text-gray-800">Aadress</p>
            <p class="text-gray-600">Maakri 19 Tallinn 10145</p>
          </div>

          <div class="text-center">
            <p class="font-medium text-gray-800">Arvelduskonto</p>
            <p class="text-gray-600">EE517700771008730313</p>
          </div>

          <div class="text-center">
            <p class="font-medium text-gray-800">Sotsiaalmeedia</p>
            <div class="flex justify-center space-x-4 mt-1">
              <a href="https://facebook.com/alimendid.ee" class="text-orange-500 hover:text-orange-600 flex items-center gap-2">
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
                Facebook
              </a>
              <a href="https://www.instagram.com/alimendid.ee" class="text-orange-500 hover:text-orange-600 flex items-center gap-2">
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
                Instagram
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- End Contact Us -->

  </main>
  <!-- ========== END MAIN CONTENT ========== -->


  <!-- //LINK - Footer -->
  <?php include '../assets/failid/komponendid/et/footer.php'; ?>

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../assets/vendor/preline/dist/index.js"></script>
  <!-- Clipboard -->
  <script src="../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../assets/js/hs-copy-clipboard-helper.js"></script>

  <script>
    window.addEventListener("load", () => {
      (function() {
        const tabsId = "hs-pro-hero-tabs";
        const tabs = HSTabs.getInstance(`#${tabsId}`, true);
        const scrollNav = HSScrollNav.getInstance(
          "#hs-pro-hero-tabs-scroll",
          true
        );

        tabs.element.on("change", ({
          el
        }) => {
          scrollNav.element.centerElement(el);
        });

        window.addEventListener(
          "resize",
          _.debounce(() => {
            scrollNav.element.centerElement(tabs.element.current);
          }, 100)
        );

        window.addEventListener("change.hs.tab", ({
          detail
        }) => {
          if (detail.payload.tabsId !== tabsId) return false;

          const tabs = document.querySelector("#hs-pro-hero-tabs-scroll");

          window.scrollTo({
            top: tabs.offsetTop,
            behavior: "smooth",
          });
        });
      })();
    });
  </script>
</body>

</html>
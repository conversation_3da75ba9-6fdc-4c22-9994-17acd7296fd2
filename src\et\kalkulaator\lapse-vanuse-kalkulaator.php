<?php

?>
<!DOCTYPE html>
<html lang="et" class="relative min-h-full">

<head>

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Lapse Vanuse Kalkulaator – Alimendid.ee</title>
    <meta name="description" content="Arvuta kiiresti ja täpselt, millal laps saab täisealiseks või 21-aastaseks. Sobib isikukoodi või sünnikuupäeva alusel.">
    <meta name="keywords" content="lapse vanuse kalkulaator, täisealisus, 21-aastaseks saamine, lapse vanuse arvutamine, isikukood, sünnikuup<PERSON>ev">
    <meta name="author" content="Alimendid.ee">

    <!-- Open Graph / Facebook -->
    <meta property="og:locale" content="et_EE" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Lapse Vanuse Kalkulaator – Alimendid.ee" />
    <meta property="og:description" content="<PERSON><PERSON><PERSON><PERSON>, millal laps saab täisealiseks ja 21-aastaseks. Si<PERSON>ta sünnikuupäev või isikukood ning saa kohe vastus." />
    <meta property="og:url" content="https://alimendid.ee/et/kalkulaator/lapse-vanuse-kalkulaator.php" />
    <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2024/lapse-vanuse-kalkulaator.jpg" />
    <meta property="og:image:alt" content="Lapse vanuse kalkulaator" />
    <meta property="og:site_name" content="Alimendid.ee" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Lapse Vanuse Kalkulaator – Alimendid.ee">
    <meta name="twitter:description" content="Arvuta lapse vanus ja 18- ja 21-aastaseks saamise kuupäevad isikukoodi või sünnikuupäeva põhjal.">
    <meta name="twitter:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2024/lapse-vanuse-kalkulaator.jpg">

    <!-- Autorlus -->
    <meta property="website:author" content="https://www.facebook.com/Alimendid.ee" />
    <meta property="website:publisher" content="https://www.facebook.com/Alimendid.ee" />
  </head>


  <!-- Favicon -->
  <link rel="shortcut icon" href="../../assets/failid/favicon/favicon.ico" />

  <!-- Font -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    rel="stylesheet" />

  <!-- CSS HS -->
  <link href="../../output.css" rel="stylesheet" />
  <link href="../../assets/css/main.min.css" rel="stylesheet" />

  <!-- Google Ads-->
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'UA-221277240-1');
  </script>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'G-C8JJCED3EQ');
  </script>

  <!-- Google Tag Manager -->
  <script>
    (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src =
        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
  </script>

  <!-- Hotjar -->
  <script>
    (function(h, o, t, j, a, r) {
      h.hj = h.hj || function() {
        (h.hj.q = h.hj.q || []).push(arguments)
      };
      h._hjSettings = {
        hjid: 3283746,
        hjsv: 6
      };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script');
      r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
  </script>

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector("html");
    const isLightOrAuto =
      localStorage.getItem("hs_theme") === "light" ||
      (localStorage.getItem("hs_theme") === "auto" &&
        !window.matchMedia("(prefers-color-scheme: dark)").matches);
    const isDarkOrAuto =
      localStorage.getItem("hs_theme") === "light" ||
      (localStorage.getItem("hs_theme") === "auto" &&
        window.matchMedia("(prefers-color-scheme: dark)").matches);

    if (isLightOrAuto && html.classList.contains("dark"))
      html.classList.remove("dark");
    else if (isDarkOrAuto && html.classList.contains("light"))
      html.classList.remove("light");
    else if (isDarkOrAuto && !html.classList.contains("dark"))
      html.classList.add("dark");
    else if (isLightOrAuto && !html.classList.contains("light"))
      html.classList.add("light");
  </script>

</head>

<body class="dark:bg-neutral-900">
  <!-- Google Tag Manager -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
  <!-- Menu -->
  <?php include '../../assets/failid/komponendid/et/menu.php'; ?>


  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">
    <div class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
      <!-- //LINK ‧ Pealkiri -->
      <!-- Hero -->
      <div class="max-w-6xl px-4 sm:px-6 lg:px-8 mx-auto">
        <div class="pt-10 md:pt-20 md:pb-20">

          <!-- Pealkiri -->
          <div class="mb-10 max-w-xl mx-auto text-center">
            <h1 class="text-4xl md:text-5xl lg:text-5xl dark:text-white font-semibold text-gray-800 ">
              Lapse vanuse kalkulaator
            </h1>
            <p class="mt-1 md:mt-2 lg:mt-2 text-xl md:text-xl text-gray-600 dark:text-gray-200">
            <p class="mt-5 text-sm md:text-lg text-gray-800 dark:text-gray-200">
              Arvuta lapse vanus ning 18- ja 21-aastaseks saamise kuupäevad
            </p>
          </div>
          <!-- End Pealkiri -->

        </div>
      </div>

      <div class="max-w-3xl mx-auto px-4 text-left text-gray-700 space-y-4">
        <p>
          Kohtusse pöördumisel tuleb elatishagis märkida, mis kuupäevani elatise välja mõistmist taotletakse. Alaealise puhul on selleks lapse 18-aastaseks saamise kuupäev ning täisealise lapse puhul 21-aastaseks saamise kuupäev.
        </p>
        <p>
          Lapse vanuse kalkulaator aitab Sul kiiresti ja korrektselt arvutada, millal saab laps 18- ja 21-aastaseks.
        </p>
        <p>
          Sisesta lapse <span class="font-semibold">sünnikuupäev</span> või <span class="font-semibold">isikukood</span> ja vajuta nuppu "Arvuta vanus".
        </p>
        <p class="text-sm text-gray-500 italic">
          Sisestatud andmeid ei salvestata ega säilitata.
        </p>
      </div>

      <div class="overflow-hidden">
        <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div class="relative mx-auto max-w-4xl grid space-y-5 sm:space-y-10">

            <!-- Form -->
            <form id="ageCalculatorForm">
              <div class="mx-auto max-w-2xl sm:flex sm:space-x-3 p-3 bg-white border border-gray-200 rounded-lg shadow-lg shadow-gray-100">
                <div class="w-full pb-2 sm:pb-0">
                  <label for="birthdate" class="block text-lg font-medium"><span class="sr-only">Sünnikuupäev</span></label>
                  <input type="text" id="birthdate" class="py-2.5 sm:py-3 px-4 block w-full border-transparent rounded-lg sm:text-md focus:border-orange-500 focus:ring-orange-500" placeholder="Sünnikuupäev" maxlength="10" oninput="formatDate(this)">
                </div>
                <div class="pt-2 sm:pt-0 sm:ps-3 border-t border-gray-200 sm:border-t-0 sm:border-s w-full">
                  <label for="personalCode" class="block text-lg font-medium"><span class="sr-only">Isikukood</span></label>
                  <input type="text" id="personalCode" class="py-2.5 sm:py-3 px-4 block w-full border-transparent rounded-lg sm:text-md focus:border-orange-500 focus:ring-orange-500" placeholder="Isikukood" maxlength="11" oninput="formatPersonalCode(this)">
                </div>
                <div class="whitespace-nowrap pt-2 sm:pt-0 grid sm:block">
                  <button type="submit" class="py-3 px-4 inline-flex justify-center items-center gap-x-2 text-md font-medium rounded-lg shadow-md text-white font-medium bg-orange-500 hover:bg-gradient-to-r hover:from-orange-500 hover:to-orange-600 hover:transition-all hover:duration-300 hover:shadow-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50 disabled:opacity-50 disabled:pointer-events-none">
                    Arvuta vanus
                  </button>
                </div>
              </div>
            </form>
            <!-- End Form -->

            <!-- Results -->
            <div id="result" class="text-center text-gray-600 dark:text-gray-300 p-4 mt-6">Tulemused kuvatakse siin, kui olete andmed sisestanud.</div>
            <!-- End Results -->

          </div>
        </div>
      </div>

      <script>
        document.getElementById('ageCalculatorForm').addEventListener('submit', function(event) {
          event.preventDefault();

          // Get the input values
          var birthdate = document.getElementById('birthdate').value;
          var personalCode = document.getElementById('personalCode').value;
          var result = document.getElementById('result');

          // Clear previous results
          result.innerHTML = '';

          // Function to manually parse dates in the format dd.mm.yyyy
          function parseDate(dateString) {
            var parts = dateString.split('.');
            var day = parseInt(parts[0], 10);
            var month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
            var year = parseInt(parts[2], 10);
            return new Date(year, month, day);
          }

          // Validate and parse the input
          var dateOfBirth;
          if (birthdate) {
            dateOfBirth = parseDate(birthdate);
          } else if (personalCode && personalCode.length === 11) {
            // Extract date from personal code
            var yearPrefix = personalCode[0] <= '2' ? '18' : personalCode[0] <= '4' ? '19' : '20';
            var year = yearPrefix + personalCode.slice(1, 3);
            var month = personalCode.slice(3, 5);
            var day = personalCode.slice(5, 7);
            dateOfBirth = new Date(year, month - 1, day);
          } else {
            result.innerHTML = '<p class="a_blog_p">Palun sisesta lapse sünnikuupäev või isikukood</p>';
            return;
          }

          // Calculate the current age
          var currentDate = new Date();
          var age = currentDate.getFullYear() - dateOfBirth.getFullYear();
          var monthDiff = currentDate.getMonth() - dateOfBirth.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < dateOfBirth.getDate())) {
            age--;
            monthDiff += 12;
          }
          var month = monthDiff - (dateOfBirth.getDate() > currentDate.getDate() ? 1 : 0);

          // Calculate the dates when the child becomes of full age and 21 years old
          var fullAgeDate = new Date(dateOfBirth);
          fullAgeDate.setFullYear(fullAgeDate.getFullYear() + 18);
          var twentyOneDate = new Date(dateOfBirth);
          twentyOneDate.setFullYear(twentyOneDate.getFullYear() + 21);

          // Format the dates
          var options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          };
          var fullAgeDateString = fullAgeDate.toLocaleDateString('et-EE', options);
          var twentyOneDateString = twentyOneDate.toLocaleDateString('et-EE', options);

          // Display the result
          result.innerHTML = `
    <div class="bg-white border border-gray-200 rounded-lg shadow-lg shadow-gray-100 p-6 dark:bg-gray-800 dark:border-gray-700 dark:shadow-gray-900">
      <p class="text-gray-800 dark:text-gray-200 mb-4">Kui laps on sündinud <span class="text-orange-500 font-semibold">${dateOfBirth.toLocaleDateString('et-EE', options)}</span>, siis on ta praegu <span class="text-orange-500 font-semibold">${age} aasta</span> ja <span class="text-orange-500 font-semibold">${month} kuu</span> vanune.</p>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="dark:bg-gray-700 p-4 rounded-lg">
          <p class="text-gray-800 dark:text-gray-400 text-md mb-1">Saab 18-aastaseks:</p>
          <p class="text-orange-500 dark:text-white text-xl font-bold">${fullAgeDateString}</p>
        </div>
        <div class="dark:bg-gray-700 p-4 rounded-lg">
          <p class="text-gray-800 dark:text-gray-400 text-md mb-1">Saab 21-aastaseks:</p>
          <p class="text-orange-500 dark:text-white text-xl font-bold">${twentyOneDateString}</p>
        </div>
      </div>
    </div>`;
        });
      </script>


      <script>
        // SÜNNIKUUPÄEV
        // See skript formateerib kuupäeva sisestust automaatselt
        // Kui kasutaja sisestab kuupäeva, lisatakse punktid õigetesse kohtadesse (pp.kk.aaaa)
        function formatDate(input) {
          // Eemalda kõik mitte-numbrid sisestusest (tähed, sümbolid jne)
          // Näiteks: "12a34b" muutub "1234"
          var value = input.value.replace(/\D/g, '');

          // Lisa punktid vastavalt sisestatud numbrite arvule
          if (value.length > 0) {
            // Kui sisestatud on 1-2 numbrit (pp), näita ainult päeva
            if (value.length <= 2) {
              input.value = value;
            }
            // Kui sisestatud on 3-4 numbrit (ppkk), lisa punkt päeva järele
            // Näiteks: "1234" muutub "12.34"
            else if (value.length <= 4) {
              input.value = value.slice(0, 2) + '.' + value.slice(2);
            }
            // Kui sisestatud on 5+ numbrit (ppkkaaaa), lisa punktid päeva ja kuu järele
            // Näiteks: "12342024" muutub "12.34.2024"
            else {
              input.value = value.slice(0, 2) + '.' + value.slice(2, 4) + '.' + value.slice(4, 8);
            }
          }
        }
      </script>

      <script>
        // ISIKUKOOD
        function formatPersonalCode(input) {
          // Eemalda kõik mitte-numbrid sisestusest
          var value = input.value.replace(/\D/g, '');

          // Piira pikkust 11 numbriga
          if (value.length > 11) {
            value = value.slice(0, 11);
          }

          // Uuenda välja väärtust
          input.value = value;
        }
      </script>


    </div>


    </div>

  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- Footer -->
  <?php include '../../assets/failid/komponendid/et/footer.php'; ?>

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../../assets/vendor/preline/dist/index.js"></script>
  <!-- Clipboard -->
  <script src="../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../../assets/js/hs-copy-clipboard-helper.js"></script>

  <script>
    window.addEventListener("load", () => {
      (function() {
        const tabsId = "hs-pro-hero-tabs";
        const tabs = HSTabs.getInstance(`#${tabsId}`, true);
        const scrollNav = HSScrollNav.getInstance(
          "#hs-pro-hero-tabs-scroll",
          true
        );

        tabs.element.on("change", ({
          el
        }) => {
          scrollNav.element.centerElement(el);
        });

        window.addEventListener(
          "resize",
          _.debounce(() => {
            scrollNav.element.centerElement(tabs.element.current);
          }, 100)
        );

        window.addEventListener("change.hs.tab", ({
          detail
        }) => {
          if (detail.payload.tabsId !== tabsId) return false;

          const tabs = document.querySelector("#hs-pro-hero-tabs-scroll");

          window.scrollTo({
            top: tabs.offsetTop,
            behavior: "smooth",
          });
        });
      })();
    });
  </script>
</body>

</html>
<?php

?>
<!DOCTYPE html>
<html lang="et" class="relative min-h-full">

<head>

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Lapse kulude kalkulaator · Alimendid.ee</title>
    <meta name="description" content="Arvuta kiiresti ja täpselt, millal laps saab täisealiseks või 21-aastaseks. Sobib isikukoodi või sünnikuupäeva alusel.">
    <meta name="keywords" content="lapse vanuse kalkulaator, täisealisus, 21-aastaseks saamine, lapse vanuse arvutamine, isikukood, sünnikuup<PERSON>ev">
    <meta name="author" content="Alimendid.ee">

    <!-- Open Graph / Facebook -->
    <meta property="og:locale" content="et_EE" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Lapse kulude kalkulaator – Alimendid.ee" />
    <meta property="og:description" content="<PERSON><PERSON><PERSON><PERSON>, millal laps saab täisealiseks ja 21-aastaseks. Si<PERSON><PERSON> sünnikuup<PERSON>ev või isikukood ning saa kohe vastus." />
    <meta property="og:url" content="https://alimendid.ee/et/kalkulaator/lapse-kulude-kalkulaator.php" />
    <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2024/lapse-kulude-kalkulaator.jpg" />
    <meta property="og:image:alt" content="Lapse vanuse kalkulaator" />
    <meta property="og:site_name" content="Alimendid.ee" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Lapse kulude kalkulaator – Alimendid.ee">
    <meta name="twitter:description" content="Arvuta lapse vanus ja 18- ja 21-aastaseks saamise kuupäevad isikukoodi või sünnikuupäeva põhjal.">
    <meta name="twitter:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2024/lapse-kulude-kalkulaator.jpg">

    <!-- Autorlus -->
    <meta property="website:author" content="https://www.facebook.com/Alimendid.ee" />
    <meta property="website:publisher" content="https://www.facebook.com/Alimendid.ee" />
  </head>


  <!-- Favicon -->
  <link rel="shortcut icon" href="../../assets/failid/favicon/favicon.ico" />

  <!-- Font -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    rel="stylesheet" />

  <!-- CSS HS -->
  <link href="../../output.css" rel="stylesheet" />
  <link href="../../assets/css/main.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../assets/failid/css/disain.css">

  <!-- ApexCharts CSS -->
  <link rel="stylesheet" href="../../assets/vendor/apexcharts/dist/apexcharts.css">

  <!-- Google Ads-->
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'UA-221277240-1');
  </script>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'G-C8JJCED3EQ');
  </script>

  <!-- Google Tag Manager -->
  <script>
    (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src =
        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
  </script>

  <!-- Hotjar -->
  <script>
    (function(h, o, t, j, a, r) {
      h.hj = h.hj || function() {
        (h.hj.q = h.hj.q || []).push(arguments)
      };
      h._hjSettings = {
        hjid: 3283746,
        hjsv: 6
      };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script');
      r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
  </script>

  <!-- ApexCharts JS -->
  <script src="../../assets/vendor/apexcharts/dist/apexcharts.min.js"></script>

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector("html");
    const isLightOrAuto =
      localStorage.getItem("hs_theme") === "light" ||
      (localStorage.getItem("hs_theme") === "auto" &&
        !window.matchMedia("(prefers-color-scheme: dark)").matches);
    const isDarkOrAuto =
      localStorage.getItem("hs_theme") === "light" ||
      (localStorage.getItem("hs_theme") === "auto" &&
        window.matchMedia("(prefers-color-scheme: dark)").matches);

    if (isLightOrAuto && html.classList.contains("dark"))
      html.classList.remove("dark");
    else if (isDarkOrAuto && html.classList.contains("light"))
      html.classList.remove("light");
    else if (isDarkOrAuto && !html.classList.contains("dark"))
      html.classList.add("dark");
    else if (isLightOrAuto && !html.classList.contains("light"))
      html.classList.add("light");
  </script>

</head>

<body class="dark:bg-neutral-900">
  <!-- Google Tag Manager -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
  <!-- Menu -->
  <?php include '../../assets/failid/komponendid/et/menu.php'; ?>


  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">

    <div class="max-w-2xl px-4 mt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
      <!-- Pealkiri -->
      <div class="mb-10 max-w-xl mx-auto text-center">
        <h1 class="text-4xl md:text-5xl lg:text-5xl dark:text-white font-semibold text-gray-800 mb-3">
          Lapse kulude kalkulaator
        </h1>
        <p class="mt-1 md:mt-2 lg:mt-3 text-xl md:text-xl text-gray-600 dark:text-gray-200">
          Arvuta lapse ülalpidamiskulude suurus
        </p>
      </div>
      <!-- End Pealkiri -->


      <div class="max-w-3xl mx-auto px-4 text-left text-gray-700 space-y-4">
        <p>
          Lapse ülalpidamiskulude kalkulaator aitab Sul arvutada lapse ülalpidamiskulude suuruse. Kalkulaatoris on eeltäidetud keskmised kulud, mida saad soovi korral muuta või täiendada. Tulemuse saad alla laadida ja kasutada seda suhtluses teise vanemaga või esitada kohtule.
        </p>

      </div>

      <!-- Chart Section - Moved to top -->
      <div class="overflow-hidden">
        <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div class="relative mx-auto max-w-4xl grid space-y-5 sm:space-y-10">

            <div class="flex flex-col justify-center items-center">
              <div id="hs-doughnut-chart"></div>

              <!-- Legend Indicator -->
              <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-x-6 gap-y-2 mt-4 sm:mt-6 text-[13px]">
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#FDBA9D"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Eluase</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#FFD1BA"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Toit</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#F5CBA7"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Transport</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#FFE0B2"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Riided</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#F8BBD0"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Haridus</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#F9A8D4"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Huvitegevus</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#E1BEE7"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Tervishoid</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#C4B5FD"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Vaba aeg</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#A7C7E7"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Side</span>
                </div>
                <div class="inline-flex items-center">
                  <span class="size-2.5 inline-block rounded-sm me-2" style="background-color:#C9E4DE"></span>
                  <span class="text-gray-600 dark:text-neutral-400">Hügieen</span>
                </div>
              </div>
              <!-- End Legend Indicator -->


            </div>

          </div>
        </div>
      </div>


      <script>
        let chart;
        let customExpenseCounter = 0;
        const maxCustomExpenses = 10;

        function updateChart() {
          const eluasemekulud = parseFloat(document.getElementById('eluasemekulud').value) || 0;
          const transportkulud = parseFloat(document.getElementById('transportkulud').value) || 0;
          const toitkulud = parseFloat(document.getElementById('toitkulud').value) || 0;
          const riidedkulud = parseFloat(document.getElementById('riidedkulud').value) || 0;
          const hariduskulud = parseFloat(document.getElementById('hariduskulud').value) || 0;
          const huvialadkulud = parseFloat(document.getElementById('huvialadkulud').value) || 0;
          const tervishoidkulud = parseFloat(document.getElementById('tervishoidkulud').value) || 0;
          const vabaaegkulud = parseFloat(document.getElementById('vabaaegkulud').value) || 0;
          const sidekulud = parseFloat(document.getElementById('sidekulud').value) || 0;
          const hugieenikulud = parseFloat(document.getElementById('hugieenikulud').value) || 0;

          // Add custom expenses
          let customTotal = 0;
          const customInputs = document.querySelectorAll('[id^="custom-expense-"]');
          customInputs.forEach(input => {
            customTotal += parseFloat(input.value) || 0;
          });

          const total = eluasemekulud + transportkulud + toitkulud + riidedkulud + hariduskulud +
            huvialadkulud + tervishoidkulud + vabaaegkulud + sidekulud + hugieenikulud + customTotal;

          // Update total amount display
          document.getElementById('total-amount').textContent = total.toFixed(0) + '€';

          if (total === 0) {
            // Show empty chart with equal segments
            if (chart) {
              chart.updateSeries([1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]);
            }
            return;
          }

          let series = [eluasemekulud, toitkulud, transportkulud, riidedkulud, hariduskulud,
            huvialadkulud, tervishoidkulud, vabaaegkulud, sidekulud, hugieenikulud
          ];

          // Add custom expense values to series
          customInputs.forEach(input => {
            series.push(parseFloat(input.value) || 0);
          });

          if (chart) {
            chart.updateSeries(series);
          }
        }

        function clearAll() {
          document.getElementById('eluasemekulud').value = '';
          document.getElementById('transportkulud').value = '';
          document.getElementById('toitkulud').value = '';
          document.getElementById('riidedkulud').value = '';
          document.getElementById('hariduskulud').value = '';
          document.getElementById('huvialadkulud').value = '';
          document.getElementById('tervishoidkulud').value = '';
          document.getElementById('vabaaegkulud').value = '';
          document.getElementById('sidekulud').value = '';
          document.getElementById('hugieenikulud').value = '';

          // Clear custom expenses
          const customInputs = document.querySelectorAll('[id^="custom-expense-"]');
          customInputs.forEach(input => {
            input.value = '';
          });

          updateChart();
        }

        function addCustomExpense() {
          if (customExpenseCounter >= maxCustomExpenses) {
            alert('Maksimaalselt saab lisada ' + maxCustomExpenses + ' kohandatud kuluartiklit.');
            return;
          }

          customExpenseCounter++;
          const expenseId = 'custom-expense-' + customExpenseCounter;
          const nameId = 'custom-name-' + customExpenseCounter;

          const listGroup = document.querySelector('.flex.flex-col.bg-white.border.border-gray-200.rounded-xl');

          const newExpenseHtml = `
            <li class="p-3 border-t border-gray-200 dark:border-neutral-700 custom-expense" data-expense-id="${customExpenseCounter}">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex flex-col justify-center items-center size-6.5 text-white rounded-full">
                    💶
                  </span>
                  <div class="grow">
                    <input type="text" id="${nameId}" placeholder="Nimeta kulu" class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1 border-0 bg-transparent p-0 focus:ring-0 focus:border-0 w-full" onchange="updateChartLabels()">
                    <input type="text" id="${nameId}-desc" placeholder="Kirjelda kulu" class="text-xs text-gray-500 dark:text-neutral-500 border-0 bg-transparent p-0 focus:ring-0 focus:border-0 w-full">
                  </div>
                </div>

                <div class="flex items-center gap-2">
                  <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                    <div class="relative w-full">
                      <input id="${expenseId}" type="number" min="0" step="1" value="0" inputmode="numeric" class="py-1.5 sm:py-1.5 ps-2 pe-8 block w-full border-stone-200 rounded-lg text-sm text-stone-800 placeholder:text-stone-500 focus:z-10 focus:border-orange-500 focus:ring-orange-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600" onchange="updateChart()">
                      <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">
                        €
                      </div>
                    </div>
                  </div>
                  <button type="button" onclick="removeCustomExpense(${customExpenseCounter})" class="p-1 text-red-500 hover:text-red-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </li>
          `;

          listGroup.insertAdjacentHTML('beforeend', newExpenseHtml);

          // Show delete button if we have custom expenses
          updateDeleteButtonVisibility();
          updateChart();
        }

        function removeCustomExpense(expenseId) {
          const expenseElement = document.querySelector(`[data-expense-id="${expenseId}"]`);
          if (expenseElement) {
            expenseElement.remove();
            updateDeleteButtonVisibility();
            updateChart();
          }
        }

        function updateDeleteButtonVisibility() {
          const customExpenses = document.querySelectorAll('.custom-expense');
          const deleteButton = document.getElementById('kustuta-kulu-nupp');

          if (customExpenses.length > 0) {
            deleteButton.classList.remove('hidden');
          } else {
            deleteButton.classList.add('hidden');
          }
        }

        function removeAllCustomExpenses() {
          const customExpenses = document.querySelectorAll('.custom-expense');
          customExpenses.forEach(expense => expense.remove());
          customExpenseCounter = 0;
          updateDeleteButtonVisibility();
          updateChart();
        }

        function updateChartLabels() {
          // This function would update chart labels when custom expense names change
          // For now, we'll keep it simple and just update the chart
          updateChart();
        }

        // Initialize chart when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
          // Wait a bit more to ensure ApexCharts is loaded
          setTimeout(function() {
            if (typeof ApexCharts === 'undefined') {
              console.error('ApexCharts is not loaded');
              return;
            }

            const options = {
              chart: {
                height: 230,
                width: 230,
                type: 'donut',
                animations: {
                  enabled: true,
                  easing: 'easeinout',
                  speed: 800
                }
              },
              plotOptions: {
                pie: {
                  donut: {
                    size: '76%'
                  }
                }
              },
              series: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], // Default equal values
              labels: [
                'Eluase',
                'Toit',
                'Transport',
                'Riided',
                'Haridus',
                'Huvialad',
                'Tervishoid',
                'Muu',
                'Side',
                'Hügieen'
              ],
              colors: [
                // Oranžid
                '#FDBA9D', // virsik
                '#FFD1BA', // aprikoos
                '#F5CBA7', // karamell
                // Roosad-lillad
                '#F8BBD0', // hele roosa
                '#F9A8D4', // maasika roosa
                '#E1BEE7', // lavendel
                // Sinakad-rohekad (pehme kontrast)
                '#A7C7E7', // pastelne taevasinine
                '#C9E4DE', // helerohekas-hall
                '#B5EAD7', // summutatud türkiis
                '#C6DEF1', // hallikas sinine
              ],
              legend: {
                show: false
              },
              dataLabels: {
                enabled: false
              },
              stroke: {
                width: 2,
                colors: ['#ffffff']
              },
              tooltip: {
                enabled: true,
                followCursor: true,
                custom: function({
                  series,
                  seriesIndex,
                  dataPointIndex,
                  w
                }) {
                  const total = series.reduce((a, b) => a + b, 0);
                  const percentage = total > 0 ? ((series[seriesIndex] / total) * 100).toFixed(1) : 0;
                  const value = series[seriesIndex];
                  const label = w.config.labels[seriesIndex];
                  const color = w.config.colors[seriesIndex];

                  return `
                    <div style="
                      background: #1f2937;
                      color: white;
                      padding: 8px 12px;
                      border-radius: 6px;
                      font-size: 12px;
                      font-weight: 500;
                      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                      border: none;
                      font-family: Inter, sans-serif;
                      white-space: nowrap;
                    ">
                      ${label}: ${value}€ (${percentage}%)
                    </div>
                  `;
                }
              },
              responsive: [{
                breakpoint: 480,
                options: {
                  chart: {
                    width: 200,
                    height: 200
                  }
                }
              }]
            };

            const chartElement = document.querySelector('#hs-doughnut-chart');
            if (chartElement) {
              chart = new ApexCharts(chartElement, options);
              chart.render().then(() => {
                console.log('Chart rendered successfully');
                updateChart();
              }).catch(error => {
                console.error('Error rendering chart:', error);
              });
            } else {
              console.error('Chart element not found');
            }
          }, 100);

          // Add event listeners for buttons
          document.getElementById('lisa-kulu-nupp').addEventListener('click', addCustomExpense);
          document.getElementById('kustuta-kulu-nupp').addEventListener('click', removeAllCustomExpenses);

          // Update all input field styles on page load
          const inputFields = document.querySelectorAll('input[type="number"]:not([id^="custom-"])');
          inputFields.forEach(input => {
            const container = input.closest('.w-full.sm\\:w-auto');
            if (container && container.classList.contains('sm:min-w-[120px]')) {
              container.classList.remove('sm:min-w-[120px]');
              container.classList.add('sm:max-w-[100px]');

              input.classList.remove('sm:py-2', 'ps-3', 'pe-12', 'sm:text-sm');
              input.classList.add('sm:py-1.5', 'ps-2', 'pe-8', 'text-sm');

              const euroSymbol = input.nextElementSibling;
              if (euroSymbol) {
                euroSymbol.classList.remove('end-2.5', 'ps-[5px]', 'text-sm');
                euroSymbol.classList.add('end-2', 'text-xs');
              }
            }
          });
        });
      </script>



      <!-- Status Card -->
      <div class="bg-white">

        <div class="p-4 space-y-6">

          <!-- Suggestions -->
          <div class="mb-2">
            <span class="text-lg font-medium text-gray-800 dark:text-neutral-200">
              Lapse kulud
            </span>
          </div>
          <!-- End Suggestions -->

          <!-- List Group -->
          <ul class="flex flex-col bg-white border border-gray-200 rounded-xl -space-y-px dark:bg-neutral-800 dark:border-neutral-700">
            <!-- Eluasemekulud -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">🏡</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Eluase</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Üür, kodulaen, kommunaalkulud</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="eluasemekulud" type="number" min="0" step="1" value="250" inputmode="numeric"
                      class="py-1.5 sm:py-1.5 px-2 block w-full border-stone-200 rounded-lg text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Toit -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">🍽️</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Toit</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Söök ja mittealkohoolsed joogid</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="toitkulud" type="number" min="0" step="1" value="140" inputmode="numeric"
                      class="py-1.5 sm:py-1.5 px-2 block w-full border-stone-200 rounded-lg text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Riided -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">👕</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Riided</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Hooajariided, jalanõud, spordivarustus</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="riidedkulud" type="number" min="0" step="1" value="50" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Huvitegevus -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">⚽</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Huvitegevus</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Trennid, ringid, laagrid</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="huvialadkulud" type="number" min="0" step="1" value="40" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Transport -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">🚌</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Transport</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Takso, ühistransport, kütus</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="transportkulud" type="number" min="0" step="1" value="30" inputmode="numeric"
                      class="py-1.5 sm:py-1.5 px-2 block w-full border-stone-200 rounded-lg text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Haridus -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">📚</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Haridus</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Koolitarbed, lasteaia kohatasu, õppematerjalid</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="hariduskulud" type="number" min="0" step="1" value="35" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>


            <!-- Tervishoid -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">💊</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Tervishoid</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Ravimid, vitamiinid, arsti- ja hambaravi omaosalus</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="tervishoidkulud" type="number" min="0" step="1" value="25" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Side -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">📱</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Side</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Telefon, internet, andmeside</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="sidekulud" type="number" min="0" step="1" value="20" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Hügieen -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">🛁</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Hügieen</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Hügieenitarbed, pesemisvahendid, mähkmed</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="hugieenikulud" type="number" min="0" step="1" value="20" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Vaba aeg -->
            <li class="p-3 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
              <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div class="flex gap-x-3 items-start">
                  <span class="mt-0.5 shrink-0 flex justify-center items-center size-6.5">🎲</span>
                  <div class="grow">
                    <h4 class="text-sm font-semibold text-gray-800 dark:text-neutral-200 mb-1">Vaba aeg</h4>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">Mänguasjad, raamatud, üritused, sünnipäevad</p>
                  </div>
                </div>
                <div class="w-full sm:w-auto sm:max-w-[100px] sm:flex-shrink-0">
                  <div class="relative w-full">
                    <input id="vabaaegkulud" type="number" min="0" step="1" value="15" inputmode="numeric"
                      class="py-1.5 sm:py-2 px-2 block w-full border-stone-200 rounded-lg sm:text-sm text-stone-800 placeholder:text-stone-500
                   text-center focus:z-10 focus:border-orange-500 focus:ring-orange-500
                   dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500
                   dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                      onchange="updateChart()">
                    <div class="absolute inset-y-0 end-2 flex items-center z-20 text-xs text-stone-400 dark:text-neutral-600">€</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <!-- End List Group -->

          <!-- Add/Remove Buttons -->
          <div class="mt-2 flex gap-2 justify-end">
            <button type="button" id="kustuta-kulu-nupp" class="py-1.5 px-2 inline-flex items-center gap-x-1 text-sm text-orange-500 font-normal rounded-xl border border-dashed border-gray-200 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 hidden" style="display: none;">
              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14" />
              </svg>
              Kustuta lisakulud
            </button>
            <button type="button" id="lisa-kulu-nupp" class="py-1.5 px-2 inline-flex items-center gap-x-1 text-sm text-orange-500 font-normal rounded-xl border border-dashed border-gray-200 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50">
              <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>
              Lisa kulu
            </button>
          </div>
          <!-- End Add/Remove Buttons -->

        </div>



        <!-- Summary -->
        <div class="p-4">
          <div class="flex justify-between items-center border-t border-dashed border-gray-200 dark:border-neutral-700 pt-4">
            <span class="text-lg font-medium text-gray-800 dark:text-neutral-200">
              Kulud kokku:
            </span>
            <span id="total-amount" class="text-3xl font-semibold text-orange-500 dark:text-orange-400">
              0.00€
            </span>
          </div>
        </div>
        <!-- End Summary -->




        <!-- Footer -->
        <div class="p-4 flex justify-end gap-x-2">
          <div class="flex-1 flex justify-end items-center gap-2">
            <button type="button" onclick="clearAll()" class="py-2 px-3 text-nowrap inline-flex justify-center items-center text-start whitespace-nowrap bg-white border border-gray-200 text-gray-800 text-lg font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
              Tühjenda
            </button>

            <button type="button"
              class="py-2 px-3 text-nowrap inline-flex justify-center items-center gap-x-2 text-start whitespace-nowrap 
         bg-orange-500 border border-orange-500 text-white text-lg font-medium rounded-lg shadow-2xs 
         align-middle hover:bg-orange-500 disabled:opacity-50 disabled:pointer-events-none 
         focus:outline-hidden focus:ring-1 focus:ring-orange-300 dark:focus:ring-orange-500">

              <!-- Lucide ikoon -->
              <svg xmlns="http://www.w3.org/2000/svg"
                class="lucide lucide-file-down w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round">

                <path d="M12 15V3" />
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <path d="m7 10 5 5 5-5" />
              </svg>

              Laadin alla
            </button>


          </div>
        </div>
        <!-- End Footer -->
      </div>
      <!-- End Status Card -->
    </div>


    </div>

  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- Footer -->
  <?php include '../../assets/failid/komponendid/et/footer.php'; ?>

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../../assets/vendor/preline/dist/index.js"></script>
  <!-- Clipboard -->
  <script src="../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../../assets/js/hs-copy-clipboard-helper.js"></script>

  <script>
    window.addEventListener("load", () => {
      (function() {
        const tabsId = "hs-pro-hero-tabs";
        const tabs = HSTabs.getInstance(`#${tabsId}`, true);
        const scrollNav = HSScrollNav.getInstance(
          "#hs-pro-hero-tabs-scroll",
          true
        );

        tabs.element.on("change", ({
          el
        }) => {
          scrollNav.element.centerElement(el);
        });

        window.addEventListener(
          "resize",
          _.debounce(() => {
            scrollNav.element.centerElement(tabs.element.current);
          }, 100)
        );

        window.addEventListener("change.hs.tab", ({
          detail
        }) => {
          if (detail.payload.tabsId !== tabsId) return false;

          const tabs = document.querySelector("#hs-pro-hero-tabs-scroll");

          window.scrollTo({
            top: tabs.offsetTop,
            behavior: "smooth",
          });
        });
      })();
    });
  </script>
</body>

</html>

</html>